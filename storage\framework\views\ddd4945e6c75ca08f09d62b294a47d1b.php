<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />
    <title>Home</title>
    <?php echo app('Illuminate\Foundation\Vite')([
        'resources/css/app.css',
        'resources/css/home/<USER>',

        // JS
        'resources/js/home/<USER>',
    ]); ?>
    <style>
        html {
            background: none;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            height: 100vh;
            /* full viewport height */
            background-image:
                linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
                url('<?php echo e(asset('images/bg.png')); ?>');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            overflow-x: hidden;
            overflow-y: hidden;
        }

        .header {
            flex-shrink: 0;
            width: 100%;
            max-width: 100%;
        }

        .main-content {
            flex: 1;
            width: 100%;
            max-width: 100%;
            overflow-x: hidden;
            padding: 0;
        }

        .footer {
            flex-shrink: 0;
            width: 100%;
            max-width: 100%;
        }

        /* Responsive adjustments for larger screens */
        @media (min-width: 1025px) {
            .main-content {
                padding: clamp(0.5rem, 1vw, 1rem) 0;
            }
        }

        @media (min-width: 1367px) {
            .main-content {
                padding: clamp(1rem, 1.5vw, 2rem) 0;
            }
        }

        /* Clickable info box styles */
        .info-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            cursor: pointer;
        }

        .info-box:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        /* home - Fixed CSS reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body,
        html {
            width: 100%;
            height: 100vh;
            overflow-x: hidden;
            overflow-y: auto;
            max-width: 100%;
            max-height: 100vh;
        }

        /* Main content layout improvements */
        .content-section-top {
            flex: 0 0 auto;
            height: clamp(180px, 35vh, 250px);
            margin-bottom: clamp(0.5rem, 2vh, 1rem);
        }

        .content-section-bottom {
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
        }

        /* Service history improvements */
        .service-history-box {
            display: flex;
            flex-direction: column;
            padding: clamp(0.5rem, 2vw, 1rem) !important;
        }

        .service-history-title {
            font-size: clamp(0.7rem, 2vw, 0.9rem) !important;
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .service-history-list {
            margin: 0;
            padding-left: clamp(0.8rem, 2vw, 1.2rem);
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
        }

        .service-item {
            font-size: clamp(0.6rem, 1.8vw, 0.8rem) !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: clamp(0.2rem, 0.5vw, 0.3rem);
        }

        /* Mitra card text improvements */
        .mitra-text {
            flex: 1;
            min-width: 0;
        }

        .mitra-company-name {
            font-size: clamp(0.6rem, 1.8vw, 0.8rem) !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: clamp(0.1rem, 0.3vw, 0.2rem);
        }

        .mitra-period {
            font-size: clamp(0.5rem, 1.5vw, 0.7rem) !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Stats box improvements */
        .stats-box {
            padding: clamp(0.3rem, 1.5vw, 0.6rem) !important;
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
            display: flex;
            flex-direction: column;
            height: auto;
        }

        .stats-number {
            font-size: clamp(1.5rem, 4vw, 2.2rem) !important;
            line-height: 1;
        }

        .stats-icon {
            max-width: clamp(35px, 6vw, 50px);
            height: auto;
        }

        .stats-label {
            font-size: clamp(0.6rem, 1.8vw, 0.8rem) !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-top: clamp(0.2rem, 0.5vw, 0.3rem);
        }

        .flip {
            animation: flip 0.5s ease-in-out;
        }

        @keyframes flip {
            0% {
                transform: rotateX(0deg);
            }

            50% {
                transform: rotateX(-90deg);
            }

            100% {
                transform: rotateX(0deg);
            }
        }

        .parent {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-auto-rows: min-content;
            gap: clamp(4px, 0.5vw, 6px);
            max-height: 100%;
            overflow: hidden;
        }

        .div2 {
            grid-column: span 5 / span 5;
            grid-row: span 2 / span 2;
            grid-row-start: 2;
        }

        .div3 {
            grid-column: span 2 / span 2;
            grid-row-start: 4;
        }

        .div4 {
            grid-column: span 3 / span 3;
            grid-column-start: 3;
            grid-row-start: 4;
        }

        .div5 {
            grid-column: span 5 / span 5;
            grid-row-start: 5;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-height: 100vh;
            justify-content: space-between;
            padding: clamp(0.3rem, 1vw, 0.8rem);
            max-width: 100%;
            width: 100%;
            overflow: hidden;
            box-sizing: border-box;
        }

        .top-section {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }

        .left-info {
            max-width: 50%;
        }

        .left-info .weather {
            display: flex;
            align-items: center;
            font-size: clamp(0.8rem, 2vw, 1rem);
            margin-bottom: clamp(0.2rem, 0.5vw, 0.3rem);
        }

        .left-info .weather img {
            height: clamp(40px, 6vw, 80px);
            margin-right: clamp(0.3rem, 1vw, 0.8rem);
        }

        .ketderajat span {
            font-size: clamp(1rem, 3vw, 2.2rem);
            font-weight: 600;
        }

        .ketderajat {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            font-size: clamp(0.8rem, 2.5vw, 1.8rem);
            line-height: 1.1;
        }

        #iconcuaca {
            width: clamp(40px, 6vw, 80px);
            height: clamp(40px, 6vw, 80px);
            display: block;
        }

        .time {
            padding: 0;
            margin: 0;
            font-size: clamp(3rem, 8vw, 7rem);
            color: #5b2991;
            line-height: 0.9;
            display: flex;
            align-items: stretch;
            font-weight: bold;
        }

        .date {
            line-height: 1.1;
            font-size: clamp(1rem, 3vw, 2.2rem);
            margin-top: clamp(0.2rem, 1vw, 0.5rem);
            color: #5b2991;
            font-weight: 600;
        }

        /* Enhanced responsive typography scaling */


        @media (min-width: 1025px) and (max-width: 1366px) {
            .time {
                font-size: clamp(5rem, 10vw, 8rem);
            }

            .date {
                font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            }

            .holiday {
                font-size: clamp(1.4rem, 2.8vw, 2rem);
            }

            #iconcuaca {
                width: clamp(70px, 6vw, 90px);
                height: clamp(70px, 6vw, 90px);
            }

            .ketderajat span {
                font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            }
        }


        .holiday {
            font-size: clamp(0.8rem, 2.5vw, 1.8rem);
            font-style: italic;
            margin-bottom: clamp(0.3rem, 1vw, 1rem);
            color: #5b2991;
            font-weight: 500;
            line-height: 1.2;
        }

        /* TOP card - hanya bagian bawah terlihat */
        .card-top {
            top: 0;
            opacity: 0.5;
            z-index: 1;
            transform: translateY(-25%);
            /* geser agar hanya bagian bawah muncul */
        }

        /* MIDDLE card - utama */
        .card-middle {
            top: 50%;
            transform: translateY(-50%);
            /* posisikan benar-benar di tengah */
            opacity: 1;
            z-index: 3;
        }

        /* BOTTOM card - hanya bagian atas terlihat */
        .card-bottom {
            bottom: 0;
            opacity: 0.5;
            z-index: 1;
            transform: translateY(25%);
            /* geser agar hanya bagian atas muncul */
        }

        @keyframes slide {

            0%,
            20% {
                transform: translateX(0);
            }

            33%,
            53% {
                transform: translateX(-100vw);
            }

            66%,
            86% {
                transform: translateX(-200vw);
            }

            100% {
                transform: translateX(0);
            }
        }

        .info-section {
            gap: clamp(0.3rem, 1vw, 0.5rem);
            display: flex;
            flex-direction: column;
        }

        .info-box {
            background: white;
            border-radius: clamp(6px, 1.5vw, 12px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            flex: 1;
            padding: clamp(0.4rem, 1.5vw, 1rem);
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
        }

        .info-box:last-child {
            margin-bottom: 0;
        }

        .info-box p {
            font-size: clamp(0.7rem, 2vw, 1.1rem);
            line-height: 1.3;
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
        }

        .info-box ul {
            margin-top: clamp(0.2rem, 0.5vw, 0.7rem);
            font-size: clamp(0.6rem, 1.8vw, 1rem);
            padding-left: clamp(0.8rem, 2vw, 1.2rem);
            line-height: 1.2;
        }

        .info-box ul li {
            margin-bottom: clamp(0.1rem, 0.3vw, 0.2rem);
        }

        .bottom-nav {
            display: flex;
            justify-content: space-around;
            border-radius: clamp(6px, 1vw, 10px);
            padding: clamp(0.3rem, 1vw, 0.6rem);
            gap: clamp(0.2rem, 0.8vw, 0.6rem);
        }

        .bottom-nav .btn {
            flex: 1;
            min-width: 0;
        }

        .bottom-nav .btn a {
            display: block;
            text-decoration: none;
            color: inherit;
        }

        .bottom-nav .neumorphism {
            padding: clamp(0.3rem, 1vw, 0.5rem);
            border-radius: clamp(8px, 2vw, 15px);
            min-height: clamp(40px, 8vw, 60px);
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .bottom-nav .imgicon {
            width: clamp(20px, 4vw, 35px);
            height: clamp(20px, 4vw, 35px);
        }

        .bottom-nav p {
            font-size: clamp(0.5rem, 1.2vw, 0.7rem);
            margin: 0;
            margin-top: clamp(0.2rem, 0.5vw, 0.3rem);
            text-align: center;
            line-height: 1.1;
        }

        /* Mobile-first responsive layout adjustments */

        /* Mobile responsive utilities */
        .d-flex { display: flex; }
        .justify-content-center { justify-content: center; }
        .align-items-center { align-items: center; }
        .mb-3 { margin-bottom: 1rem; }

        /* Ensure no horizontal overflow */
        .main-content, .header, .footer {
            width: 100%;
            max-width: 100%;
            overflow-x: hidden;
        }

        /* Mobile touch targets */
        .info-box, .mitra-card, .btn {
            min-height: clamp(44px, 8vw, 60px);
            touch-action: manipulation;
        }

        /* Mobile-first breakpoints */
        @media (max-width: 767px) {
            .col-12 {
                flex: 0 0 100%;
                max-width: 100%;
            }

            .mb-3 { margin-bottom: 1rem; }

            .row {
                margin-left: -8px;
                margin-right: -8px;
            }

            [class*="col-"] {
                padding-left: 8px;
                padding-right: 8px;
            }

            /* Mobile-specific layout adjustments */
            .main-content {
                padding: clamp(0.3rem, 2vw, 0.8rem) 0;
            }

            .slider-wrapper {
                height: clamp(100px, 18vw, 200px);
            }

            .time {
                font-size: clamp(2.5rem, 7vw, 4rem);
            }

            .date {
                font-size: clamp(0.8rem, 2.5vw, 1.5rem);
            }

            .left-info,
            .right-logo {
                max-width: 100%;
            }

            .mitra-cards {
                flex-direction: column;
            }

            .content-section-top {
                height: clamp(150px, 30vh, 200px);
                margin-bottom: clamp(0.3rem, 1vh, 0.5rem);
            }

            .holiday {
                font-size: clamp(1rem, 3vw, 1.4rem);
                margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
            }

            .service-history-title {
                font-size: clamp(0.6rem, 2.5vw, 0.8rem) !important;
            }

            .service-item {
                font-size: clamp(0.5rem, 2vw, 0.7rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.5rem, 2vw, 0.7rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.4rem, 1.8vw, 0.6rem) !important;
            }

            .stats-number {
                font-size: clamp(1.2rem, 5vw, 1.8rem) !important;
            }

            .stats-label {
                font-size: clamp(0.5rem, 2vw, 0.7rem) !important;
            }
        }

        /* Tablet and up */
        @media (min-width: 768px) {
            .col-md-4 {
                flex: 0 0 33.333333%;
                max-width: 33.333333%;
            }

            .col-md-6 {
                flex: 0 0 50%;
                max-width: 50%;
            }

            .col-md-8 {
                flex: 0 0 66.666667%;
                max-width: 66.666667%;
            }

            .mb-md-0 { margin-bottom: 0; }
        }

        /* Tablet landscape and small desktop */
        @media (min-width: 769px) and (max-width: 1024px) {
            .mitra-cards-stack {
                height: clamp(100px, 12vw, 120px);
            }

            .content-section-top {
                height: clamp(160px, 32vh, 220px);
                margin-bottom: clamp(0.4rem, 1.5vh, 0.7rem);
            }

            .service-history-title {
                font-size: clamp(0.7rem, 2.2vw, 0.9rem) !important;
            }

            .service-item {
                font-size: clamp(0.6rem, 1.9vw, 0.8rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.6rem, 1.9vw, 0.8rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.5rem, 1.6vw, 0.7rem) !important;
            }

            .stats-number {
                font-size: clamp(1.8rem, 4.5vw, 2.3rem) !important;
            }

            .stats-label {
                font-size: clamp(0.6rem, 1.9vw, 0.8rem) !important;
            }
        }

        @media (min-width: 1025px) and (max-width: 1366px) {

            .mitra-cards-stack {
                height: clamp(600px, 100vw, 800px);
            }

            .slider-wrapper {
                height: clamp(210px, 12vw, 230px);
            }

            .content-section-top {
                height: clamp(170px, 33vh, 230px);
                margin-bottom: clamp(0.5rem, 1.5vh, 0.8rem);
            }

            .service-history-title {
                font-size: clamp(0.7rem, 1.8vw, 0.9rem) !important;
            }

            .service-item {
                font-size: clamp(0.6rem, 1.5vw, 0.8rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.6rem, 1.5vw, 0.8rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.5rem, 1.3vw, 0.7rem) !important;
            }

            .stats-number {
                font-size: clamp(1.8rem, 3.5vw, 2.3rem) !important;
            }

            .stats-label {
                font-size: clamp(0.6rem, 1.5vw, 0.8rem) !important;
            }
        }

        @media (min-width: 1000px) {
            .container {
                height: 100vh;
                max-height: 100vh;
                overflow: hidden;
                padding: clamp(0.2rem, 0.5vw, 0.4rem);
            }

            .parent {
                gap: clamp(3px, 0.3vw, 4px);
                max-height: calc(100vh - 1rem);
                overflow: hidden;
            }


            .mitra-cards-stack {
                height: clamp(500px, 40vw, 7023px);
            }

            .slider-wrapper {
                height: clamp(150px, 8vw, 180px);
            }



            .date {
                font-size: clamp(1.2rem, 2.5vw, 1.8rem);
            }

            .holiday {
                font-size: clamp(1rem, 2vw, 1.5rem);
            }

            #iconcuaca {
                width: clamp(50px, 5vw, 70px);
                height: clamp(50px, 5vw, 70px);
            }

            .ketderajat span {
                font-size: clamp(1.2rem, 2.5vw, 1.8rem);
            }

            .content-section-top {
                height: clamp(140px, 28vh, 180px);
                margin-bottom: clamp(0.3rem, 1vh, 0.5rem);
            }

            .service-history-title {
                font-size: clamp(0.6rem, 1.2vw, 0.8rem) !important;
            }

            .service-item {
                font-size: clamp(0.5rem, 1vw, 0.7rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.5rem, 1vw, 0.7rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.4rem, 0.9vw, 0.6rem) !important;
            }

            .stats-number {
                font-size: clamp(1.5rem, 2.5vw, 2rem) !important;
            }

            .stats-label {
                font-size: clamp(0.5rem, 1vw, 0.7rem) !important;
            }
        }

        @media (min-width: 1367px) {

            .mitra-cards-stack {
                height: clamp(603px, 4232vw, 1280px);
            }

            .slider-wrapper {
                height: clamp(170px, 8vw, 210px);
            }

            .content-section-top {
                height: clamp(160px, 30vh, 200px);
                margin-bottom: clamp(0.4rem, 1.2vh, 0.6rem);
            }

            .service-history-title {
                font-size: clamp(0.7rem, 1.1vw, 0.9rem) !important;
            }

            .service-item {
                font-size: clamp(0.6rem, 0.9vw, 0.8rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.6rem, 0.9vw, 0.8rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.5rem, 0.8vw, 0.7rem) !important;
            }

            .stats-number {
                font-size: clamp(1.8rem, 2.2vw, 2.3rem) !important;
            }

            .stats-label {
                font-size: clamp(0.6rem, 0.9vw, 0.8rem) !important;
            }
        }

        .btnmenu {
            background: white;
            border-radius: 25px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            flex: 1;
            margin: 0.5rem;
        }

        .mitra-cards-stack {
            position: relative;
            height: clamp(80px, 8vw, 120px);
            /* responsive height for card stacking */
            width: 100%;
            margin-bottom: clamp(0.5rem, 2vw, 1rem);
        }

        .mitra-card {
            position: absolute;
            left: 0;
            width: 100%;
            background: white;
            border-radius: clamp(6px, 1.5vw, 15px);
            padding: clamp(0.4rem, 1.2vw, 0.7rem);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.12);
            display: flex;
            align-items: center;
            gap: clamp(0.4rem, 1.5vw, 1.2rem);
            transition: all 0.3s ease;
            font-size: clamp(0.6rem, 2vw, 1rem);
        }

        .mitra-card img {
            width: clamp(25px, 4vw, 50px);
            height: clamp(25px, 4vw, 50px);
            object-fit: contain;
            flex-shrink: 0;
        }

        .mitra-card div {
            flex: 1;
            min-width: 0;
        }

        .mitra-card p {
            margin: 0;
            line-height: 1.2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
        }

        /* Card atas (bagian bawahnya saja yang tampak) */
        .card-top {
            top: 0;
            transform: translateY(-30%);
            opacity: 0.5;
            z-index: 1;
        }

        /* Card tengah (utama) */
        .card-middle {
            margin-left: 20px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 1;
            z-index: 3;
        }

        /* Card bawah (bagian atasnya saja yang tampak) */
        .card-bottom {
            bottom: 0;
            transform: translateY(30%);
            opacity: 0.5;
            z-index: 1;
        }

        /* animasi card */
        .slider-wrapper {
            position: relative;
            width: 100%;
        }

        .slider-track {
            display: flex;
            width: 300%;
            transition: transform 0.5s ease-in-out;
        }

        .slide {
            width: 100%;
            flex-shrink: 0;
            border-radius: 10px;
        }

        .slider-dots {
            bottom: 10px;
        }

        .dot {
            height: 10px;
            width: 10px;
            margin: 0 5px;
            background-color: #bbb;
            border-radius: 50%;
            display: inline-block;
            border: none;
        }

        .dot.active,
        .dot:hover {
            background-color: #717171;
        }

        /* image slide */
        .slider-wrapper {
            height: clamp(120px, 20vw, 330px);
            width: 100%;
            max-width: 100%;
            overflow: hidden;
            border-radius: clamp(6px, 1.5vw, 12px);
        }

        .slider-track {
            display: flex;
            height: 100%;
            width: 100%;
        }

        .slide {
            width: 100%;
            height: 100%;
            flex-shrink: 0;
            border-radius: clamp(6px, 1.5vw, 12px);
            overflow: hidden;
            position: relative;
        }

        .slide-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
            border-radius: clamp(6px, 1.5vw, 12px);
        }

        /* Additional layout improvements */
        .row {
            margin-left: 0;
            margin-right: 0;
        }

        [class*="col-"] {
            padding-left: clamp(4px, 1vw, 8px);
            padding-right: clamp(4px, 1vw, 8px);
        }

        /* Ensure no overflow on any elements */
        * {
            max-width: 100%;
            box-sizing: border-box;
        }

        /* Improved tagline styling */
        .right-logo .tagline {
            font-size: clamp(0.6rem, 1.8vw, 0.9rem) !important;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }

        /* Very small screens (mobile phones in portrait) */
        @media (max-width: 480px) {
            body {
                padding: clamp(0.2rem, 2vw, 0.5rem);
            }

            .tagline {
                font-size: clamp(0.4rem, 3vw, 0.6rem) !important;
            }

            .content-section-top {
                height: clamp(120px, 25vh, 160px);
            }

            .holiday {
                font-size: clamp(0.9rem, 3.5vw, 1.2rem);
            }

            #iconcuaca {
                width: clamp(45px, 7vw, 60px);
                height: clamp(45px, 7vw, 60px);
            }

            .ketderajat span {
                font-size: clamp(1.1rem, 4vw, 1.5rem);
            }

            .service-history-title {
                font-size: clamp(0.5rem, 3vw, 0.7rem) !important;
            }

            .service-item {
                font-size: clamp(0.4rem, 2.5vw, 0.6rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.4rem, 2.5vw, 0.6rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.3rem, 2vw, 0.5rem) !important;
            }

            .stats-number {
                font-size: clamp(1rem, 6vw, 1.5rem) !important;
            }

            .stats-label {
                font-size: clamp(0.4rem, 2.5vw, 0.6rem) !important;
            }

            .row {
                margin-left: -4px;
                margin-right: -4px;
            }

            [class*="col-"] {
                padding-left: 4px;
                padding-right: 4px;
            }
        }

        /* Holiday text improvements */
        .holiday {
            font-size: clamp(1.2rem, 3.5vw, 1.8rem);
            font-style: italic;
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
            color: #5b2991;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Ensure info boxes don't overflow */
        .info-box {
            overflow: hidden;
            word-wrap: break-word;
        }

        .stats-box:last-child {
            margin-bottom: 0;
        }
    </style>
</head>

<body>
    <div class="div1 header">
        <div class="right-logo">
            <div class="logo-line">
                <img src="<?php echo e(asset('images/logo.png')); ?>" alt="Logo" />
                <p class="slogan">PT. PUTERA WIBOWO BORNEO</p>
            </div>
            <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"</div>
        </div>
    </div>
    <div class="main-content mt-8">
        <div class="div2">
            <div class="row px-10" style="height: 100%">
                <div class="col-12 col-md-4">
                    <div class="row">
                        <div class="col-3 col-md-6 m-0 p-0 d-flex justify-content-center align-items-center"><img id="iconcuaca" src="<?php echo e(asset('assets/icon/sun.png')); ?>"
                                alt="weather" />
                        </div>
                        <div class="col-9 col-md-6 ketderajat">
                            <span id="derajat">33°</span>
                            <span id="ketcuaca">Cerah</span>
                        </div>
                    </div>
                    <div id="time" class="time">08:30</div>
                    <div id="date" class="date">Senin, 12 Juli 2025</div>
                    <div id="holiday" class="holiday">Hari pendidikan nasional</div>
                </div>
                <div class="col-12 col-md-8 position-relative">
                    <div class="slider-wrapper overflow-hidden rounded-sm">
                        <div class="slider-track d-flex transition" id="sliderTrack">

                            
                            <div class="slide flex-fill position-relative">
                                <a href="#">
                                    <img src="<?php echo e(asset('images/sample5.png')); ?>" alt="" class="slide-image">
                                </a>
                            </div>
                            <div class="slide flex-fill position-relative">
                                <a href="#">
                                    <img src="<?php echo e(asset('images/sample5.png')); ?>" alt="" class="slide-image">
                                </a>
                            </div>
                            <div class="slide flex-fill position-relative">
                                <a href="#">
                                    <img src="<?php echo e(asset('images/sample5.png')); ?>" alt="" class="slide-image">
                                </a>
                            </div>
                            
                        </div>
                    </div>
                    <!-- Bulatan navigasi -->
                    <div class="slider-dots text-center position-absolute w-100" style="bottom: 10px;">
                        <button class="dot" onclick="moveToSlide(0)"></button>
                        <button class="dot" onclick="moveToSlide(1)"></button>
                        <button class="dot" onclick="moveToSlide(2)"></button>
                    </div>
                </div>
            </div>
        </div>
        <div class="div2 mt-10">
            <div class="row mt-10 px-10 pt-10">
                <div class="col-12 col-md-4 mb-3 mb-md-0">
                    <div class="mitra-cards-stack">
                        <div class="mitra-card card-top">
                            <img src="<?php echo e(asset('images/mitra/ppa.png')); ?>" alt="IMK" />
                            <div>
                                <p style="font-size:  0.9rem !important;" class="font-bold">PT. PUTRA PERKASA ABADI</p>
                                <p style="font-size:  0.9rem !important;">SEP 2021 - SEKARANG</p>
                            </div>
                        </div>

                        <div class="mitra-card card-middle">
                            <img src="<?php echo e(asset('images/mitra/IMK.png')); ?>" alt="IMK" />
                            <div>
                                <p style="font-size:  0.9rem !important;" class="font-bold">PT. INDO MURO KENCANA</p>
                                <p style="font-size:  0.9rem !important;">SEP 2021 - SEKARANG</p>
                            </div>
                        </div>

                        <div class="mitra-card card-bottom">
                            <img src="<?php echo e(asset('images/mitra/udu.jpg')); ?>" alt="IMK" />
                            <div>
                                <p style="font-size:  0.9rem !important;" class="font-bold">PT. UNGGUL DINAMIKA UTAMA
                                </p>
                                <p style="font-size:  0.9rem !important;">SEP 2021 - SEKARANG</p>
                            </div>
                        </div>
                    </div>

                </div>
                <div class="col-12 col-md-4 mb-3 mb-md-0">
                    <div class="info-box p-4" style="background-color: #effcff;">
                        <p>Lihat Riwayat Servis Terakhir</p>
                        <ul>
                            <li>HD7856 HM - 12 Juni , 08:00</li>
                            <li>HD78353 HM - 10 Juni , 08:00</li>
                            <li>H57160AMM - 01 Juni , 08:00</li>
                        </ul>
                    </div>
                </div>
                <div class="col-12 col-md-4">
                    <div class="info-section">
                        <a href="<?php echo e(route('invoice')); ?>" class="info-box p-2"
                            style="background-color: #effcff; text-decoration: none; color: inherit; display: block; transition: transform 0.2s ease, box-shadow 0.2s ease;">
                            <div class="row">
                                <div class="col-6">
                                    <p class="h4 font-bold" style="font-size: 2.5rem !important;">20</p>
                                </div>
                                <div class="col">
                                    <img style="max-width: 60px;" src="<?php echo e(asset('assets/icon/invoice.png')); ?>"
                                        class="img-fluid ${3|rounded-top,rounded-right,rounded-bottom,rounded-left,rounded-circle,|}"
                                        alt="Image">
                                </div>
                            </div>
                            <p class="font-bold">Utang invoice</p>
                        </a>
                        <a href="<?php echo e(route('pending-po')); ?>" class="info-box p-2"
                            style="background-color: #effcff; text-decoration: none; color: inherit; display: block; transition: transform 0.2s ease, box-shadow 0.2s ease;">
                            <div class="row">
                                <div class="col-6">
                                    <p class="h4 font-bold" style="font-size: 2.5rem !important;">40</p>
                                </div>
                                <div class="col">
                                    <img style="max-width: 60px;" src="<?php echo e(asset('assets/icon/pending.png')); ?>"
                                        class="img-fluid ${3|rounded-top,rounded-right,rounded-bottom,rounded-left,rounded-circle,|}"
                                        alt="Image">
                                </div>
                            </div>
                            <p class="font-bold">Pending PO</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="pt-4 footer">
        <div class="top-section">
            <div class="left-info">
            </div>
        </div>
        <div class="bottom-nav">
            <div class="flex justify-center items-center gap-4 p-4 rounded-3xl border">
                <div class="btn">
                    <a href="<?php echo e(route('calender')); ?>">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-yellow" src="<?php echo e(asset('assets/icon/calendar.png')); ?>"
                                        alt="">
                                </div>
                            </div>
                            <p class="mt-0 text-sm font-semibold text-white pt-2 text-center">Kalender</p>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="<?php echo e(route('profile')); ?>">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-purple" src="<?php echo e(asset('assets/icon/workplace.png')); ?>"
                                        alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Profile</div>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="<?php echo e(route('produk')); ?>">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-gold" src="<?php echo e(asset('assets/icon/newproduct.png')); ?>"
                                        alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Product</div>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="<?php echo e(route('informasi')); ?>">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-green" src="<?php echo e(asset('assets/icon/info.png')); ?>" alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Informasi</div>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="<?php echo e(route('layanan')); ?>">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-orange" src="<?php echo e(asset('assets/icon/support.png')); ?>"
                                        alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Layanan</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const totalSlides = document.querySelectorAll('.slide').length;
        const sliderTrack = document.getElementById('sliderTrack');
        const dots = document.querySelectorAll('.dot');

        function moveToSlide(index) {
            currentSlide = index;
            const offset = -index * 100;
            sliderTrack.style.transform = `translateX(${offset}%)`;

            dots.forEach(dot => dot.classList.remove('active'));
            dots[index].classList.add('active');
        }

        // Auto slide
        setInterval(() => {
            currentSlide = (currentSlide + 1) % totalSlides;
            moveToSlide(currentSlide);
        }, 5000); // 5 detik
    </script>
</body>

</html><?php /**PATH C:\xampp\htdocs\pwbapp\resources\views/home.blade.php ENDPATH**/ ?>