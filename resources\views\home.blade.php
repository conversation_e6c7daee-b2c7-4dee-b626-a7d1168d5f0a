<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=no, viewport-fit=cover" />
    <title>Home</title>
    @vite([
        'resources/css/app.css',
        'resources/css/home/<USER>',

        // JS
        'resources/js/home/<USER>',
    ])
    <style>
        html {
            background: none;
        }

        body {
            display: flex;
            flex-direction: column;
            min-height: 100vh;
            height: 100vh;
            width: 100vw;
            max-width: 100vw;
            /* full viewport height */
            background-image:
                linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
                url('{{ asset('images/bg.png') }}');
            background-size: cover;
            background-repeat: no-repeat;
            background-position: center;
            background-attachment: fixed;
            overflow-x: hidden;
            overflow-y: hidden;
            margin: 0;
            padding: 0;
            position: relative;
        }

        .header {
            flex-shrink: 0;
            width: 100%;
            max-width: 100%;
        }

        .main-content {
            flex: 1;
            width: 100%;
            max-width: 100%;
            overflow-x: hidden;
            overflow-y: auto;
            padding: 0;
            display: flex;
            flex-direction: column;
            min-height: 0;
        }

        .footer {
            flex-shrink: 0;
            width: 100%;
            max-width: 100%;
        }

        /* Responsive adjustments for larger screens */
        @media (min-width: 1025px) {
            .main-content {
                padding: clamp(0.5rem, 1vw, 1rem) 0;
            }
        }

        @media (min-width: 1367px) {
            .main-content {
                padding: clamp(1rem, 1.5vw, 2rem) 0;
            }
        }

        /* Clickable info box styles */
        .info-box:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            cursor: pointer;
        }

        .info-box:active {
            transform: translateY(0);
            box-shadow: 0 2px 6px rgba(0, 0, 0, 0.1);
        }

        /* Android-optimized CSS reset */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
            -webkit-tap-highlight-color: transparent;
            -webkit-touch-callout: none;
            -webkit-user-select: none;
            -moz-user-select: none;
            -ms-user-select: none;
            user-select: none;
        }

        body,
        html {
            width: 100%;
            height: 100vh;
            overflow-x: hidden;
            overflow-y: hidden;
            max-width: 100%;
            max-height: 100vh;
            -webkit-font-smoothing: antialiased;
            -moz-osx-font-smoothing: grayscale;
            text-rendering: optimizeLegibility;
        }

        /* Main content layout improvements */
        .content-section-top {
            flex: 0 0 auto;
            height: clamp(180px, 35vh, 250px);
            margin-bottom: clamp(0.5rem, 2vh, 1rem);
        }

        .content-section-bottom {
            flex: 1;
            min-height: 0;
            display: flex;
            flex-direction: column;
        }

        /* Service history improvements */
        .service-history-box {
            display: flex;
            flex-direction: column;
            padding: clamp(0.5rem, 2vw, 1rem) !important;
        }

        .service-history-title {
            font-size: clamp(0.7rem, 2vw, 0.9rem) !important;
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        .service-history-list {
            margin: 0;
            padding-left: clamp(0.8rem, 2vw, 1.2rem);
            flex: 1;
            display: flex;
            flex-direction: column;
            justify-content: space-around;
        }

        .service-item {
            font-size: clamp(0.6rem, 1.8vw, 0.8rem) !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: clamp(0.2rem, 0.5vw, 0.3rem);
        }

        /* Mitra card text improvements */
        .mitra-text {
            flex: 1;
            min-width: 0;
        }

        .mitra-company-name {
            font-size: clamp(0.6rem, 1.8vw, 0.8rem) !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            margin-bottom: clamp(0.1rem, 0.3vw, 0.2rem);
        }

        .mitra-period {
            font-size: clamp(0.5rem, 1.5vw, 0.7rem) !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Android-optimized stats boxes */
        .stats-box {
            padding: clamp(12px, 3vw, 16px) !important;
            margin-bottom: clamp(8px, 2vw, 12px);
            display: flex;
            flex-direction: column;
            min-height: 80px;
            background: white;
            border-radius: clamp(8px, 2vw, 12px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            text-decoration: none;
            color: inherit;
            transition: all 0.3s ease;
            -webkit-transition: all 0.3s ease;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }

        .stats-box:hover, .stats-box:active {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
        }

        .stats-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: clamp(4px, 1vw, 8px);
        }

        .stats-number {
            font-size: clamp(1.8rem, 5vw, 2.5rem) !important;
            line-height: 1;
            font-weight: bold;
            color: #5b2991;
        }

        .stats-icon {
            flex-shrink: 0;
        }

        .stats-icon img {
            width: clamp(32px, 6vw, 48px);
            height: clamp(32px, 6vw, 48px);
            object-fit: contain;
        }

        .stats-label {
            font-size: clamp(0.7rem, 2vw, 0.9rem) !important;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            font-weight: 600;
            margin: 0;
        }

        .flip {
            animation: flip 0.5s ease-in-out;
        }

        @keyframes flip {
            0% {
                transform: rotateX(0deg);
            }

            50% {
                transform: rotateX(-90deg);
            }

            100% {
                transform: rotateX(0deg);
            }
        }

        .parent {
            display: grid;
            grid-template-columns: repeat(5, 1fr);
            grid-auto-rows: min-content;
            gap: clamp(4px, 0.5vw, 6px);
            max-height: 100%;
            overflow: hidden;
        }

        .div2 {
            width: 100%;
            max-width: 100%;
            flex: 1;
            display: flex;
            flex-direction: column;
            margin-bottom: clamp(0.5rem, 2vw, 1rem);
        }

        .div3 {
            grid-column: span 2 / span 2;
            grid-row-start: 4;
        }

        .div4 {
            grid-column: span 3 / span 3;
            grid-column-start: 3;
            grid-row-start: 4;
        }

        .div5 {
            grid-column: span 5 / span 5;
            grid-row-start: 5;
        }

        .container {
            display: flex;
            flex-direction: column;
            height: 100vh;
            max-height: 100vh;
            justify-content: space-between;
            padding: clamp(0.3rem, 1vw, 0.8rem);
            max-width: 100%;
            width: 100%;
            overflow: hidden;
            box-sizing: border-box;
        }

        .top-section {
            display: flex;
            justify-content: space-between;
            flex-wrap: wrap;
        }

        .left-info {
            max-width: 50%;
        }

        .left-info .weather {
            display: flex;
            align-items: center;
            font-size: clamp(0.8rem, 2vw, 1rem);
            margin-bottom: clamp(0.2rem, 0.5vw, 0.3rem);
        }

        .left-info .weather img {
            height: clamp(40px, 6vw, 80px);
            margin-right: clamp(0.3rem, 1vw, 0.8rem);
        }

        .ketderajat span {
            font-size: clamp(1rem, 3vw, 2.2rem);
            font-weight: 600;
        }

        .ketderajat {
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
            font-size: clamp(0.8rem, 2.5vw, 1.8rem);
            line-height: 1.1;
        }

        #iconcuaca {
            width: clamp(40px, 6vw, 80px);
            height: clamp(40px, 6vw, 80px);
            display: block;
        }

        .time {
            padding: 0;
            margin: 0;
            font-size: clamp(3rem, 8vw, 7rem);
            color: #5b2991;
            line-height: 0.9;
            display: flex;
            align-items: stretch;
            font-weight: bold;
        }

        .date {
            line-height: 1.1;
            font-size: clamp(1rem, 3vw, 2.2rem);
            margin-top: clamp(0.2rem, 1vw, 0.5rem);
            color: #5b2991;
            font-weight: 600;
        }

        /* Enhanced responsive typography scaling */


        @media (min-width: 1025px) and (max-width: 1366px) {
            .time {
                font-size: clamp(5rem, 10vw, 8rem);
            }

            .date {
                font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            }

            .holiday {
                font-size: clamp(1.4rem, 2.8vw, 2rem);
            }

            #iconcuaca {
                width: clamp(70px, 6vw, 90px);
                height: clamp(70px, 6vw, 90px);
            }

            .ketderajat span {
                font-size: clamp(1.8rem, 3.5vw, 2.5rem);
            }
        }


        .holiday {
            font-size: clamp(0.8rem, 2.5vw, 1.8rem);
            font-style: italic;
            margin-bottom: clamp(0.3rem, 1vw, 1rem);
            color: #5b2991;
            font-weight: 500;
            line-height: 1.2;
        }

        /* TOP card - hanya bagian bawah terlihat */
        .card-top {
            top: 0;
            opacity: 0.5;
            z-index: 1;
            transform: translateY(-25%);
            /* geser agar hanya bagian bawah muncul */
        }

        /* MIDDLE card - utama */
        .card-middle {
            top: 50%;
            transform: translateY(-50%);
            /* posisikan benar-benar di tengah */
            opacity: 1;
            z-index: 3;
        }

        /* BOTTOM card - hanya bagian atas terlihat */
        .card-bottom {
            bottom: 0;
            opacity: 0.5;
            z-index: 1;
            transform: translateY(25%);
            /* geser agar hanya bagian atas muncul */
        }

        @keyframes slide {

            0%,
            20% {
                transform: translateX(0);
            }

            33%,
            53% {
                transform: translateX(-100vw);
            }

            66%,
            86% {
                transform: translateX(-200vw);
            }

            100% {
                transform: translateX(0);
            }
        }

        .info-section {
            gap: clamp(0.3rem, 1vw, 0.5rem);
            display: flex;
            flex-direction: column;
        }

        .info-box {
            background: white;
            border-radius: clamp(6px, 1.5vw, 12px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            flex: 1;
            padding: clamp(0.4rem, 1.5vw, 1rem);
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
        }

        .info-box:last-child {
            margin-bottom: 0;
        }

        .info-box p {
            font-size: clamp(0.7rem, 2vw, 1.1rem);
            line-height: 1.3;
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
        }

        .info-box ul {
            margin-top: clamp(0.2rem, 0.5vw, 0.7rem);
            font-size: clamp(0.6rem, 1.8vw, 1rem);
            padding-left: clamp(0.8rem, 2vw, 1.2rem);
            line-height: 1.2;
        }

        .info-box ul li {
            margin-bottom: clamp(0.1rem, 0.3vw, 0.2rem);
        }

        .bottom-nav {
            display: flex;
            justify-content: space-around;
            border-radius: clamp(6px, 1vw, 10px);
            padding: clamp(0.3rem, 1vw, 0.6rem);
            gap: clamp(0.2rem, 0.8vw, 0.6rem);
        }

        .bottom-nav .btn {
            flex: 1;
            min-width: 60px;
            min-height: 60px;
        }

        .bottom-nav .btn a {
            display: block;
            text-decoration: none;
            color: inherit;
            min-height: 60px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            padding: clamp(4px, 1vw, 8px);
        }

        .bottom-nav .neumorphism {
            padding: clamp(8px, 2vw, 12px);
            border-radius: clamp(8px, 2vw, 15px);
            min-height: 48px;
            min-width: 48px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: clamp(4px, 1vw, 6px);
        }

        .bottom-nav .imgicon {
            width: clamp(24px, 5vw, 32px);
            height: clamp(24px, 5vw, 32px);
        }

        .bottom-nav p {
            font-size: clamp(0.5rem, 1.2vw, 0.7rem);
            margin: 0;
            text-align: center;
            line-height: 1.1;
            white-space: nowrap;
        }

        /* Mobile-first responsive layout adjustments */

        /* Mobile responsive utilities */
        .d-flex { display: flex; }
        .justify-content-center { justify-content: center; }
        .align-items-center { align-items: center; }
        .mb-3 { margin-bottom: 1rem; }

        /* Android-optimized layout structure */
        .top-content-section {
            flex: 0 0 auto;
            padding: clamp(0.5rem, 2vw, 1rem);
            margin-bottom: clamp(0.5rem, 2vw, 1rem);
        }

        .bottom-content-section {
            flex: 1;
            padding: clamp(0.5rem, 2vw, 1rem);
            min-height: 0;
        }

        .content-row {
            display: flex;
            flex-direction: column;
            gap: clamp(0.5rem, 2vw, 1rem);
            height: 100%;
        }

        .weather-time-section {
            flex: 1;
            display: flex;
            flex-direction: column;
            gap: clamp(0.3rem, 1vw, 0.5rem);
        }

        .weather-info {
            display: flex;
            align-items: center;
            gap: clamp(0.5rem, 2vw, 1rem);
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
        }

        .weather-icon {
            flex-shrink: 0;
        }

        .weather-details {
            flex: 1;
        }

        .slider-section {
            flex: 1;
            position: relative;
            min-height: clamp(120px, 25vw, 200px);
        }

        .content-grid {
            display: flex;
            flex-direction: column;
            gap: clamp(0.5rem, 2vw, 1rem);
            height: 100%;
        }

        .mitra-section, .service-section, .stats-section {
            flex: 1;
            min-height: 0;
        }

        .stats-grid {
            display: flex;
            flex-direction: column;
            gap: clamp(0.3rem, 1vw, 0.5rem);
        }

        .stats-content {
            display: flex;
            align-items: center;
            justify-content: space-between;
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
        }

        /* Ensure no horizontal overflow */
        .main-content, .header, .footer {
            width: 100%;
            max-width: 100%;
            overflow-x: hidden;
        }

        /* Android touch targets - 48dp minimum (48px at 160dpi) */
        .info-box, .mitra-card, .btn, .stats-box, .dot {
            min-height: 48px;
            min-width: 48px;
            touch-action: manipulation;
            cursor: pointer;
        }

        /* Ensure clickable areas are properly sized */
        .stats-box, .info-box {
            padding: clamp(12px, 3vw, 16px);
            display: flex;
            flex-direction: column;
            justify-content: center;
            align-items: flex-start;
        }

        .btn a {
            display: block;
            min-height: 48px;
            display: flex;
            flex-direction: column;
            align-items: center;
            justify-content: center;
            text-decoration: none;
            color: inherit;
        }

        /* Bottom navigation touch targets */
        .bottom-nav .btn {
            min-height: 60px;
            min-width: 60px;
        }

        .bottom-nav .neumorphism {
            min-height: 48px;
            min-width: 48px;
            padding: clamp(8px, 2vw, 12px);
        }

        /* Android Mobile Portrait (320px - 767px) */
        @media (max-width: 767px) {
            body {
                background-attachment: scroll; /* Better performance on Android */
            }

            .content-row {
                flex-direction: column;
                gap: clamp(0.3rem, 1.5vw, 0.8rem);
            }

            .weather-time-section {
                order: 1;
                padding-bottom: clamp(0.5rem, 2vw, 1rem);
            }

            .slider-section {
                order: 2;
                min-height: clamp(100px, 20vw, 150px);
            }

            .content-grid {
                gap: clamp(0.3rem, 1.5vw, 0.8rem);
            }

            .mitra-section {
                order: 1;
            }

            .service-section {
                order: 2;
            }

            .stats-section {
                order: 3;
            }

            .stats-grid {
                flex-direction: row;
                gap: clamp(0.3rem, 1vw, 0.5rem);
            }

            .stats-box {
                flex: 1;
                min-width: 0;
            }

            .time {
                font-size: clamp(2.5rem, 8vw, 4rem);
                line-height: 0.8;
            }

            .date {
                font-size: clamp(0.8rem, 2.5vw, 1.2rem);
            }

            .holiday {
                font-size: clamp(0.7rem, 2vw, 1rem);
            }
        }

            .left-info,
            .right-logo {
                max-width: 100%;
            }

            .mitra-cards {
                flex-direction: column;
            }

            .content-section-top {
                height: clamp(150px, 30vh, 200px);
                margin-bottom: clamp(0.3rem, 1vh, 0.5rem);
            }

            .holiday {
                font-size: clamp(1rem, 3vw, 1.4rem);
                margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
            }

            .service-history-title {
                font-size: clamp(0.6rem, 2.5vw, 0.8rem) !important;
            }

            .service-item {
                font-size: clamp(0.5rem, 2vw, 0.7rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.5rem, 2vw, 0.7rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.4rem, 1.8vw, 0.6rem) !important;
            }

            .stats-number {
                font-size: clamp(1.2rem, 5vw, 1.8rem) !important;
            }

            .stats-label {
                font-size: clamp(0.5rem, 2vw, 0.7rem) !important;
            }
        }

        /* Android Landscape Mode */
        @media (max-width: 767px) and (orientation: landscape) {
            .content-row {
                flex-direction: row;
                gap: clamp(0.5rem, 2vw, 1rem);
            }

            .weather-time-section {
                flex: 0 0 40%;
                order: 1;
            }

            .slider-section {
                flex: 1;
                order: 2;
                min-height: clamp(80px, 15vw, 120px);
            }

            .content-grid {
                flex-direction: row;
                gap: clamp(0.3rem, 1vw, 0.5rem);
            }

            .mitra-section, .service-section, .stats-section {
                flex: 1;
            }

            .stats-grid {
                flex-direction: column;
            }

            .time {
                font-size: clamp(2rem, 6vw, 3rem);
            }

            .date {
                font-size: clamp(0.7rem, 2vw, 1rem);
            }

            .holiday {
                font-size: clamp(0.6rem, 1.8vw, 0.9rem);
            }
        }

        /* Tablet Portrait and up */
        @media (min-width: 768px) {
            .content-row {
                flex-direction: row;
                gap: clamp(1rem, 3vw, 2rem);
            }

            .weather-time-section {
                flex: 0 0 35%;
            }

            .slider-section {
                flex: 1;
                min-height: clamp(150px, 25vw, 250px);
            }

            .content-grid {
                flex-direction: row;
                gap: clamp(1rem, 3vw, 2rem);
            }

            .mitra-section, .service-section, .stats-section {
                flex: 1;
            }

            .stats-grid {
                flex-direction: column;
                gap: clamp(0.5rem, 1.5vw, 1rem);
            }
        }

        /* Tablet landscape and small desktop */
        @media (min-width: 769px) and (max-width: 1024px) {
            .mitra-cards-stack {
                height: clamp(100px, 12vw, 120px);
            }

            .content-section-top {
                height: clamp(160px, 32vh, 220px);
                margin-bottom: clamp(0.4rem, 1.5vh, 0.7rem);
            }

            .service-history-title {
                font-size: clamp(0.7rem, 2.2vw, 0.9rem) !important;
            }

            .service-item {
                font-size: clamp(0.6rem, 1.9vw, 0.8rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.6rem, 1.9vw, 0.8rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.5rem, 1.6vw, 0.7rem) !important;
            }

            .stats-number {
                font-size: clamp(1.8rem, 4.5vw, 2.3rem) !important;
            }

            .stats-label {
                font-size: clamp(0.6rem, 1.9vw, 0.8rem) !important;
            }
        }

        @media (min-width: 1025px) and (max-width: 1366px) {

            .mitra-cards-stack {
                height: clamp(600px, 100vw, 800px);
            }

            .slider-wrapper {
                height: clamp(210px, 12vw, 230px);
            }

            .content-section-top {
                height: clamp(170px, 33vh, 230px);
                margin-bottom: clamp(0.5rem, 1.5vh, 0.8rem);
            }

            .service-history-title {
                font-size: clamp(0.7rem, 1.8vw, 0.9rem) !important;
            }

            .service-item {
                font-size: clamp(0.6rem, 1.5vw, 0.8rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.6rem, 1.5vw, 0.8rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.5rem, 1.3vw, 0.7rem) !important;
            }

            .stats-number {
                font-size: clamp(1.8rem, 3.5vw, 2.3rem) !important;
            }

            .stats-label {
                font-size: clamp(0.6rem, 1.5vw, 0.8rem) !important;
            }
        }

        @media (min-width: 1000px) {
            .container {
                height: 100vh;
                max-height: 100vh;
                overflow: hidden;
                padding: clamp(0.2rem, 0.5vw, 0.4rem);
            }

            .parent {
                gap: clamp(3px, 0.3vw, 4px);
                max-height: calc(100vh - 1rem);
                overflow: hidden;
            }


            .mitra-cards-stack {
                height: clamp(500px, 40vw, 7023px);
            }

            .slider-wrapper {
                height: clamp(150px, 8vw, 180px);
            }



            .date {
                font-size: clamp(1.2rem, 2.5vw, 1.8rem);
            }

            .holiday {
                font-size: clamp(1rem, 2vw, 1.5rem);
            }

            #iconcuaca {
                width: clamp(50px, 5vw, 70px);
                height: clamp(50px, 5vw, 70px);
            }

            .ketderajat span {
                font-size: clamp(1.2rem, 2.5vw, 1.8rem);
            }

            .content-section-top {
                height: clamp(140px, 28vh, 180px);
                margin-bottom: clamp(0.3rem, 1vh, 0.5rem);
            }

            .service-history-title {
                font-size: clamp(0.6rem, 1.2vw, 0.8rem) !important;
            }

            .service-item {
                font-size: clamp(0.5rem, 1vw, 0.7rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.5rem, 1vw, 0.7rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.4rem, 0.9vw, 0.6rem) !important;
            }

            .stats-number {
                font-size: clamp(1.5rem, 2.5vw, 2rem) !important;
            }

            .stats-label {
                font-size: clamp(0.5rem, 1vw, 0.7rem) !important;
            }
        }

        @media (min-width: 1367px) {

            .mitra-cards-stack {
                height: clamp(603px, 4232vw, 1280px);
            }

            .slider-wrapper {
                height: clamp(170px, 8vw, 210px);
            }

            .content-section-top {
                height: clamp(160px, 30vh, 200px);
                margin-bottom: clamp(0.4rem, 1.2vh, 0.6rem);
            }

            .service-history-title {
                font-size: clamp(0.7rem, 1.1vw, 0.9rem) !important;
            }

            .service-item {
                font-size: clamp(0.6rem, 0.9vw, 0.8rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.6rem, 0.9vw, 0.8rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.5rem, 0.8vw, 0.7rem) !important;
            }

            .stats-number {
                font-size: clamp(1.8rem, 2.2vw, 2.3rem) !important;
            }

            .stats-label {
                font-size: clamp(0.6rem, 0.9vw, 0.8rem) !important;
            }
        }

        .btnmenu {
            background: white;
            border-radius: 25px;
            padding: 1rem;
            box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
            flex: 1;
            margin: 0.5rem;
        }

        .mitra-cards-stack {
            position: relative;
            height: clamp(70px, 12vw, 100px);
            width: 100%;
            margin-bottom: clamp(0.5rem, 2vw, 1rem);
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }

        .mitra-card {
            position: absolute;
            left: 0;
            width: 100%;
            background: white;
            border-radius: clamp(8px, 2vw, 12px);
            padding: clamp(8px, 2vw, 12px);
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
            display: flex;
            align-items: center;
            gap: clamp(8px, 2vw, 16px);
            transition: all 0.3s ease;
            -webkit-transition: all 0.3s ease;
            font-size: clamp(0.6rem, 2vw, 1rem);
            min-height: 60px;
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        .mitra-card img {
            width: clamp(32px, 6vw, 48px);
            height: clamp(32px, 6vw, 48px);
            object-fit: contain;
            flex-shrink: 0;
            border-radius: 4px;
        }

        .mitra-text {
            flex: 1;
            min-width: 0;
            display: flex;
            flex-direction: column;
            gap: clamp(2px, 0.5vw, 4px);
        }

        .mitra-company-name {
            margin: 0;
            line-height: 1.2;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            font-weight: 600;
            font-size: clamp(0.6rem, 2vw, 0.9rem) !important;
        }

        .mitra-period {
            margin: 0;
            line-height: 1.1;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: nowrap;
            opacity: 0.7;
            font-size: clamp(0.5rem, 1.8vw, 0.7rem) !important;
        }

        /* Card atas (bagian bawahnya saja yang tampak) */
        .card-top {
            top: 0;
            transform: translateY(-30%);
            opacity: 0.5;
            z-index: 1;
        }

        /* Card tengah (utama) */
        .card-middle {
            margin-left: 20px;
            top: 50%;
            transform: translateY(-50%);
            opacity: 1;
            z-index: 3;
        }

        /* Card bawah (bagian atasnya saja yang tampak) */
        .card-bottom {
            bottom: 0;
            transform: translateY(30%);
            opacity: 0.5;
            z-index: 1;
        }

        /* animasi card */
        .slider-wrapper {
            position: relative;
            width: 100%;
        }

        .slider-track {
            display: flex;
            width: 300%;
            transition: transform 0.5s ease-in-out;
        }

        .slide {
            width: 100%;
            flex-shrink: 0;
            border-radius: 10px;
        }

        .slider-dots {
            position: absolute;
            bottom: 10px;
            left: 50%;
            transform: translateX(-50%);
            display: flex;
            gap: 8px;
            z-index: 10;
        }

        .dot {
            height: clamp(8px, 2vw, 12px);
            width: clamp(8px, 2vw, 12px);
            background-color: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
            border: none;
            cursor: pointer;
            transition: all 0.3s ease;
            -webkit-transition: all 0.3s ease;
            touch-action: manipulation;
            min-width: 44px;
            min-height: 44px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .dot::before {
            content: '';
            width: clamp(8px, 2vw, 12px);
            height: clamp(8px, 2vw, 12px);
            background-color: rgba(255, 255, 255, 0.6);
            border-radius: 50%;
        }

        .dot.active::before,
        .dot:hover::before {
            background-color: rgba(255, 255, 255, 1);
        }

        /* image slide */
        .slider-wrapper {
            height: clamp(120px, 20vw, 330px);
            width: 100%;
            max-width: 100%;
            overflow: hidden;
            border-radius: clamp(6px, 1.5vw, 12px);
            position: relative;
            background-color: #f0f0f0;
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
        }

        .slider-track {
            display: flex;
            height: 100%;
            width: 300%;
            transition: transform 0.5s ease-in-out;
            -webkit-transition: -webkit-transform 0.5s ease-in-out;
        }

        .slide {
            width: 33.333%;
            height: 100%;
            flex-shrink: 0;
            border-radius: clamp(6px, 1.5vw, 12px);
            overflow: hidden;
            position: relative;
        }

        .slide-image {
            width: 100%;
            height: 100%;
            object-fit: cover;
            display: block;
            border-radius: clamp(6px, 1.5vw, 12px);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
        }

        /* Additional layout improvements */
        .row {
            margin-left: 0;
            margin-right: 0;
        }

        [class*="col-"] {
            padding-left: clamp(4px, 1vw, 8px);
            padding-right: clamp(4px, 1vw, 8px);
        }

        /* Ensure no overflow on any elements */
        * {
            max-width: 100%;
            box-sizing: border-box;
        }

        /* Improved tagline styling */
        .right-logo .tagline {
            font-size: clamp(0.6rem, 1.8vw, 0.9rem) !important;
            line-height: 1.2;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
            max-width: 100%;
        }

        /* Android Small Phones (320px - 480px) */
        @media (max-width: 480px) {
            body {
                padding: clamp(0.2rem, 1.5vw, 0.4rem);
            }

            .top-content-section, .bottom-content-section {
                padding: clamp(0.3rem, 1.5vw, 0.6rem);
            }

            .tagline {
                font-size: clamp(0.4rem, 3vw, 0.6rem) !important;
            }

            .holiday {
                font-size: clamp(0.7rem, 3vw, 1rem);
            }

            #iconcuaca {
                width: clamp(35px, 6vw, 50px);
                height: clamp(35px, 6vw, 50px);
            }

            .ketderajat span {
                font-size: clamp(0.9rem, 3.5vw, 1.3rem);
            }

            .time {
                font-size: clamp(2rem, 7vw, 3.5rem) !important;
            }

            .date {
                font-size: clamp(0.7rem, 2.2vw, 1rem) !important;
            }

            .service-history-title {
                font-size: clamp(0.5rem, 2.8vw, 0.7rem) !important;
            }

            .service-item {
                font-size: clamp(0.4rem, 2.2vw, 0.6rem) !important;
            }

            .mitra-company-name {
                font-size: clamp(0.4rem, 2.2vw, 0.6rem) !important;
            }

            .mitra-period {
                font-size: clamp(0.3rem, 1.8vw, 0.5rem) !important;
            }

            .stats-number {
                font-size: clamp(1rem, 5vw, 1.4rem) !important;
            }

            .stats-label {
                font-size: clamp(0.4rem, 2.2vw, 0.6rem) !important;
            }

            .slider-section {
                min-height: clamp(80px, 18vw, 120px);
            }

            .mitra-cards-stack {
                height: clamp(60px, 12vw, 90px);
            }
        }

        /* Android Orientation Change Support */
        @media screen and (orientation: landscape) {
            body {
                height: 100vh;
                max-height: 100vh;
            }

            .main-content {
                height: calc(100vh - 120px); /* Account for header and footer */
            }
        }

        /* Cross-Device Android Compatibility */

        /* Android Chrome and WebView optimizations */
        body {
            -webkit-overflow-scrolling: touch;
            -webkit-font-smoothing: antialiased;
            -webkit-text-size-adjust: 100%;
            -ms-text-size-adjust: 100%;
        }

        /* Hardware acceleration for smooth animations */
        .slider-wrapper, .mitra-cards-stack, .info-box, .stats-box, .mitra-card {
            -webkit-transform: translateZ(0);
            transform: translateZ(0);
            -webkit-backface-visibility: hidden;
            backface-visibility: hidden;
            will-change: transform;
        }

        /* Android-specific screen size optimizations */

        /* Small Android phones (320px - 360px) */
        @media (max-width: 360px) {
            .top-content-section, .bottom-content-section {
                padding: clamp(4px, 1vw, 8px);
            }

            .weather-info {
                gap: clamp(4px, 1vw, 8px);
            }

            .time {
                font-size: clamp(1.8rem, 6vw, 2.8rem) !important;
            }

            .mitra-cards-stack {
                height: clamp(50px, 10vw, 70px);
            }

            .stats-box {
                min-height: 60px;
                padding: clamp(8px, 2vw, 12px) !important;
            }
        }

        /* Medium Android phones (361px - 480px) */
        @media (min-width: 361px) and (max-width: 480px) {
            .content-row, .content-grid {
                gap: clamp(8px, 2vw, 12px);
            }

            .time {
                font-size: clamp(2.2rem, 7vw, 3.5rem) !important;
            }

            .mitra-cards-stack {
                height: clamp(60px, 11vw, 85px);
            }
        }

        /* Large Android phones/small tablets (481px - 768px) */
        @media (min-width: 481px) and (max-width: 768px) {
            .content-row {
                gap: clamp(12px, 3vw, 16px);
            }

            .content-grid {
                gap: clamp(12px, 3vw, 16px);
            }

            .time {
                font-size: clamp(2.8rem, 8vw, 4.5rem) !important;
            }

            .mitra-cards-stack {
                height: clamp(70px, 12vw, 95px);
            }

            .stats-box {
                min-height: 90px;
            }
        }

        /* Holiday text improvements */
        .holiday {
            font-size: clamp(1.2rem, 3.5vw, 1.8rem);
            font-style: italic;
            margin-bottom: clamp(0.3rem, 1vw, 0.5rem);
            color: #5b2991;
            white-space: nowrap;
            overflow: hidden;
            text-overflow: ellipsis;
        }

        /* Ensure info boxes don't overflow */
        .info-box {
            overflow: hidden;
            word-wrap: break-word;
        }

        .stats-box:last-child {
            margin-bottom: 0;
        }
    </style>
</head>

<body>
    <div class="div1 header">
        <div class="right-logo">
            <div class="logo-line">
                <img src="{{ asset('images/logo.png') }}" alt="Logo" />
                <p class="slogan">PT. PUTERA WIBOWO BORNEO</p>
            </div>
            <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"</div>
        </div>
    </div>
    <div class="main-content">
        <!-- Top Section: Weather, Time, and Image Slider -->
        <div class="top-content-section">
            <div class="content-row">
                <!-- Weather and Time Section -->
                <div class="weather-time-section">
                    <div class="weather-info">
                        <div class="weather-icon">
                            <img id="iconcuaca" src="{{asset('assets/icon/sun.png')}}" alt="weather" />
                        </div>
                        <div class="weather-details ketderajat">
                            <span id="derajat">33°</span>
                            <span id="ketcuaca">Cerah</span>
                        </div>
                    </div>
                    <div id="time" class="time">08:30</div>
                    <div id="date" class="date">Senin, 12 Juli 2025</div>
                    <div id="holiday" class="holiday">Hari pendidikan nasional</div>
                </div>

                <!-- Image Slider Section -->
                <div class="slider-section">
                    <div class="slider-wrapper">
                        <div class="slider-track" id="sliderTrack">
                            <div class="slide">
                                <a href="#">
                                    <img src="{{asset('images/sample5.png')}}" alt="" class="slide-image">
                                </a>
                            </div>
                            <div class="slide">
                                <a href="#">
                                    <img src="{{asset('images/sample5.png')}}" alt="" class="slide-image">
                                </a>
                            </div>
                            <div class="slide">
                                <a href="#">
                                    <img src="{{asset('images/sample5.png')}}" alt="" class="slide-image">
                                </a>
                            </div>
                        </div>
                    </div>
                    <!-- Navigation dots -->
                    <div class="slider-dots">
                        <button class="dot" onclick="moveToSlide(0)"></button>
                        <button class="dot" onclick="moveToSlide(1)"></button>
                        <button class="dot" onclick="moveToSlide(2)"></button>
                    </div>
                </div>
            </div>
        </div>
        <!-- Bottom Section: Mitra Cards and Info Boxes -->
        <div class="bottom-content-section">
            <div class="content-grid">
                <!-- Mitra Cards Section -->
                <div class="mitra-section">
                    <div class="mitra-cards-stack">
                        <div class="mitra-card card-top">
                            <img src="{{asset('images/mitra/ppa.png')}}" alt="PPA" />
                            <div class="mitra-text">
                                <p class="mitra-company-name font-bold">PT. PUTRA PERKASA ABADI</p>
                                <p class="mitra-period">SEP 2021 - SEKARANG</p>
                            </div>
                        </div>

                        <div class="mitra-card card-middle">
                            <img src="{{asset('images/mitra/IMK.png')}}" alt="IMK" />
                            <div class="mitra-text">
                                <p class="mitra-company-name font-bold">PT. INDO MURO KENCANA</p>
                                <p class="mitra-period">SEP 2021 - SEKARANG</p>
                            </div>
                        </div>

                        <div class="mitra-card card-bottom">
                            <img src="{{asset('images/mitra/udu.jpg')}}" alt="UDU" />
                            <div class="mitra-text">
                                <p class="mitra-company-name font-bold">PT. UNGGUL DINAMIKA UTAMA</p>
                                <p class="mitra-period">SEP 2021 - SEKARANG</p>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Service History Section -->
                <div class="service-section">
                    <div class="info-box service-history-box" style="background-color: #effcff;">
                        <p class="service-history-title">Lihat Riwayat Servis Terakhir</p>
                        <ul class="service-history-list">
                            <li class="service-item">HD7856 HM - 12 Juni , 08:00</li>
                            <li class="service-item">HD78353 HM - 10 Juni , 08:00</li>
                            <li class="service-item">H57160AMM - 01 Juni , 08:00</li>
                        </ul>
                    </div>
                </div>

                <!-- Stats Section -->
                <div class="stats-section">
                    <div class="stats-grid">
                        <a href="{{ route('invoice') }}" class="stats-box" style="background-color: #effcff;">
                            <div class="stats-content">
                                <div class="stats-number">20</div>
                                <div class="stats-icon">
                                    <img src="{{asset('assets/icon/invoice.png')}}" alt="Invoice">
                                </div>
                            </div>
                            <p class="stats-label">Utang invoice</p>
                        </a>
                        <a href="{{ route('pending-po') }}" class="stats-box" style="background-color: #effcff;">
                            <div class="stats-content">
                                <div class="stats-number">40</div>
                                <div class="stats-icon">
                                    <img src="{{asset('assets/icon/pending.png')}}" alt="Pending">
                                </div>
                            </div>
                            <p class="stats-label">Pending PO</p>
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="pt-4 footer">
        <div class="top-section">
            <div class="left-info">
            </div>
        </div>
        <div class="bottom-nav">
            <div class="flex justify-center items-center gap-4 p-4 rounded-3xl border">
                <div class="btn">
                    <a href="{{route('calender')}}">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-yellow" src="{{ asset('assets/icon/calendar.png') }}"
                                        alt="">
                                </div>
                            </div>
                            <p class="mt-0 text-sm font-semibold text-white pt-2 text-center">Kalender</p>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="{{route('profile')}}">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-purple" src="{{ asset('assets/icon/workplace.png') }}"
                                        alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Profile</div>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="{{route('produk')}}">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-gold" src="{{ asset('assets/icon/newproduct.png') }}"
                                        alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Product</div>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="{{route('informasi')}}">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-green" src="{{ asset('assets/icon/info.png') }}" alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Informasi</div>
                        </div>
                    </a>
                </div>
                <div class="btn">
                    <a href="{{route('layanan')}}">
                        <div class="flex flex-col items-center">
                            <div class="neumorphism p-2 flex items-center justify-center cursor-pointer h-100vw">
                                <div class="text-white text-6xl px-2">
                                    <img class="imgicon imgicon-orange" src="{{ asset('assets/icon/support.png') }}"
                                        alt="">
                                </div>
                            </div>
                            <div class="mt-0 text-sm font-semibold text-white pt-2 text-center">Layanan</div>
                        </div>
                    </a>
                </div>
            </div>
        </div>
    </div>

    <script>
        let currentSlide = 0;
        const totalSlides = document.querySelectorAll('.slide').length;
        const sliderTrack = document.getElementById('sliderTrack');
        const dots = document.querySelectorAll('.dot');

        function moveToSlide(index) {
            currentSlide = index;
            const offset = -index * 33.333; // 33.333% for 3 slides
            sliderTrack.style.transform = `translateX(${offset}%)`;
            sliderTrack.style.webkitTransform = `translateX(${offset}%)`;

            dots.forEach(dot => dot.classList.remove('active'));
            if (dots[index]) {
                dots[index].classList.add('active');
            }
        }

        // Initialize first slide
        if (dots.length > 0) {
            dots[0].classList.add('active');
        }

        // Auto slide
        let slideInterval = setInterval(() => {
            currentSlide = (currentSlide + 1) % totalSlides;
            moveToSlide(currentSlide);
        }, 5000);

        // Android orientation change handling
        function handleOrientationChange() {
            // Pause auto-slide during orientation change
            clearInterval(slideInterval);

            // Resume after orientation change is complete
            setTimeout(() => {
                slideInterval = setInterval(() => {
                    currentSlide = (currentSlide + 1) % totalSlides;
                    moveToSlide(currentSlide);
                }, 5000);
            }, 500);
        }

        // Listen for orientation changes (Android specific)
        window.addEventListener('orientationchange', handleOrientationChange);
        window.addEventListener('resize', handleOrientationChange);

        // Prevent zoom on double tap (Android)
        let lastTouchEnd = 0;
        document.addEventListener('touchend', function (event) {
            const now = (new Date()).getTime();
            if (now - lastTouchEnd <= 300) {
                event.preventDefault();
            }
            lastTouchEnd = now;
        }, false);
    </script>
</body>

</html>