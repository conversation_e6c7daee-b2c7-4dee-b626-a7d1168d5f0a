<!DOCTYPE html>
<html lang="id">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Kalender PT. Putera Wibowo Borneo</title>
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="style.css">
    @vite([
        'resources/css/app.css',
        'resources/css/calender.css',
        'resources/js/app.js'
    ])
</head>
<style>
    html {
        background: none;
    }

    body {
        padding: 2%;
        display: flex;
        flex-direction: column;
        /* min-height: 100%; */
        /* full viewport height */
        background-image:
            linear-gradient(to bottom, rgba(255, 255, 255, 1) 0%, rgba(255, 255, 255, 0) 80%),
            url('{{ asset('images/bg.png') }}');
        background-size: cover;
        background-repeat: no-repeat;
        background-position: center;
    }

    .header {
        flex-shrink: 0;
    }

    /* Company Header Styling */
    .company-header-section {
        display: flex;
        justify-content: flex-end;
        padding: 0 1rem 1rem 0;
        position: relative;
        z-index: 10;
    }

    .company-header-section .header {
        background: rgba(255, 255, 255, 0.9);
        backdrop-filter: blur(10px);
        border-radius: 15px;
        padding: 1rem 1.5rem;
        border: 1px solid rgba(255, 255, 255, 0.3);
        box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
    }
</style>

<body>

    <div class="page-container">
        <!-- Company Header - Preserved as requested -->
        <div class="company-header-section">
            <div class="div1 header">
                <div class="right-logo">
                    <div class="logo-line">
                        <img src="{{ asset('images/logo.png') }}" alt="Logo" />
                        <p class="slogan">PT. PUTERA WIBOWO BORNEO</p>
                    </div>
                    <div class="tagline">"Mitra Terpercaya dalam Solusi Alat Berat & Layanan Teknologi Industri"
                    </div>
                </div>
            </div>
        </div>


        <main class="modern-calendar-layout">
            <!-- Left Panel - Dark Theme with Calendar -->
            <section class="left-panel">
                <div class="calendar-header">
                    <h1 class="calendar-title">KALENDER</h1>
                    <p class="calendar-subtitle">DESIGN <span class="highlight">MODERN</span></p>
                </div>

                <div class="month-display">
                    <div class="month-navigation">
                        <span class="nav-arrow" id="prevMonth">&lt;</span>
                        <h2 class="current-month">AGUSTUS 2025</h2>
                        <span class="nav-arrow" id="nextMonth">&gt;</span>
                    </div>
                </div>

                <div class="main-calendar-container">
                    <div class="main-calendar-grid">
                        <div class="day-header">SUN</div>
                        <div class="day-header">MON</div>
                        <div class="day-header">TUE</div>
                        <div class="day-header">WED</div>
                        <div class="day-header">THU</div>
                        <div class="day-header">FRI</div>
                        <div class="day-header">SAT</div>

                        <div class="date-cell inactive" data-date="29" data-month="07">29</div>
                        <div class="date-cell inactive" data-date="30" data-month="07">30</div>
                        <div class="date-cell clickable" data-date="01" data-month="08">01</div>
                        <div class="date-cell clickable" data-date="02" data-month="08">02</div>
                        <div class="date-cell clickable" data-date="03" data-month="08">03</div>
                        <div class="date-cell clickable" data-date="04" data-month="08">04</div>
                        <div class="date-cell clickable" data-date="05" data-month="08">05</div>

                        <div class="date-cell sunday clickable" data-date="06" data-month="08">06</div>
                        <div class="date-cell featured clickable" data-date="07" data-month="08">07</div>
                        <div class="date-cell clickable" data-date="08" data-month="08">08</div>
                        <div class="date-cell clickable" data-date="09" data-month="08">09</div>
                        <div class="date-cell clickable" data-date="10" data-month="08">10</div>
                        <div class="date-cell clickable" data-date="11" data-month="08">11</div>
                        <div class="date-cell clickable" data-date="12" data-month="08">12</div>

                        <div class="date-cell sunday clickable" data-date="13" data-month="08">13</div>
                        <div class="date-cell featured clickable" data-date="14" data-month="08">14</div>
                        <div class="date-cell clickable" data-date="15" data-month="08">15</div>
                        <div class="date-cell clickable" data-date="16" data-month="08">16</div>
                        <div class="date-cell holiday clickable" data-date="17" data-month="08">17</div>
                        <div class="date-cell clickable" data-date="18" data-month="08">18</div>
                        <div class="date-cell clickable" data-date="19" data-month="08">19</div>

                        <div class="date-cell sunday clickable" data-date="20" data-month="08">20</div>
                        <div class="date-cell featured clickable" data-date="21" data-month="08">21</div>
                        <div class="date-cell clickable" data-date="22" data-month="08">22</div>
                        <div class="date-cell clickable" data-date="23" data-month="08">23</div>
                        <div class="date-cell clickable" data-date="24" data-month="08">24</div>
                        <div class="date-cell highlighted clickable" data-date="25" data-month="08">25</div>
                        <div class="date-cell clickable" data-date="26" data-month="08">26</div>

                        <div class="date-cell sunday clickable" data-date="27" data-month="08">27</div>
                        <div class="date-cell featured clickable" data-date="28" data-month="08">28</div>
                        <div class="date-cell clickable" data-date="29" data-month="08">29</div>
                        <div class="date-cell clickable" data-date="30" data-month="08">30</div>
                        <div class="date-cell clickable" data-date="31" data-month="08">31</div>
                        <div class="date-cell inactive" data-date="01" data-month="09">01</div>
                        <div class="date-cell inactive" data-date="02" data-month="09">02</div>
                    </div>
                </div>

                <div class="contact-info">
                    <p>www.puterawibowo.com</p>
                    <p><EMAIL></p>
                </div>
            </section>

            <!-- Right Panel - Activity and Reminder Management -->
            <section class="right-panel">
                <!-- Activity Display Section -->
                <div id="activityDisplay" class="activity-display" style="display: none;">
                    <div class="activity-header">
                        <h3 id="selectedDateTitle">Kegiatan untuk tanggal</h3>
                        <button id="closeActivityBtn" class="close-activity-btn">&times;</button>
                    </div>
                    <div id="activityList" class="activity-list">
                        <!-- Activities will be populated here by JavaScript -->
                    </div>
                </div>

                <div class="mini-calendar">
                    <h3>Juli 2025</h3>
                    <div class="mini-grid">
                        <span>M</span><span>S</span><span>S</span><span>R</span><span>K</span><span>J</span><span>S</span>
                        <span class="inactive">29</span><span
                            class="inactive">30</span><span>01</span><span>02</span><span>03</span><span>04</span><span
                            class="sunday">05</span>
                        <span
                            class="sunday">06</span><span>07</span><span>08</span><span>09</span><span>10</span><span>11</span><span>12</span>
                        <span
                            class="sunday">13</span><span>14</span><span>15</span><span>16</span><span>17</span><span>18</span><span>19</span>
                        <span
                            class="sunday">20</span><span>21</span><span>22</span><span>23</span><span>24</span><span>25</span><span>26</span>
                        <span class="sunday">27</span><span>28</span><span>29</span><span>30</span><span>31</span><span
                            class="inactive">01</span><span class="inactive">02</span>
                    </div>
                </div>

                <div class="reminder-form-section">
                    <div class="reminder-form-container">
                        <div id="reminderForms">
                            <form id="reminderForm" class="reminder-form">
                                <p class="form-title">Pengingat Tugas/Kegiatan</p>
                                <div>
                                    <input type="text" id="activityName" name="activityName"
                                        class="form-input"
                                        placeholder="Masukkan Kegiatan" required />
                                </div>
                                <div>
                                    <input type="date" id="activityDate" name="activityDate"
                                        class="form-input" required />
                                </div>
                                <div>
                                    <div class="form-row">
                                        <input type="time" id="activityTime" name="activityTime"
                                            class="form-input" required />
                                        <button type="submit" id="addReminderBtn" class="add-btn">+</button>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>

                <!-- Reminder List Display -->
                <div class="reminder-list-section">
                    <h3 class="reminder-list-title">List Reminder</h3>
                    <div id="reminderListDisplay" class="reminder-list-container">
                        <!-- Pre-populated reminders will be displayed here -->
                    </div>
                </div>
            </section>
        </main>

        <a href="{{ route('home') }}" class="home-button neumorphism">
            <img class="imgicon-purple" src="{{ asset('assets/icon/home.png') }}" alt="Home" width="40" height="40">
            </img>
            <span>HOME</span>
        </a>
    </div>

    <!-- Modern Calendar Styles -->
    <style>
        /* Modern Split-Screen Layout */
        .modern-calendar-layout {
            display: flex;
            height: calc(100vh - 8rem);
            max-height: calc(100vh - 8rem);
            overflow: hidden;
            width: 100%;
            box-sizing: border-box;
            gap: 0;
        }

        /* Left Panel - Dark Theme */
        .left-panel {
            flex: 0 0 60%;
            background: linear-gradient(135deg, #2D1B69 0%, #1A0F3A 100%);
            color: white;
            padding: clamp(1.5rem, 3vw, 2.5rem);
            display: flex;
            flex-direction: column;
            justify-content: space-between;
            position: relative;
            overflow: hidden;
        }

        .left-panel::before {
            content: '';
            position: absolute;
            top: 0;
            right: 0;
            width: 100px;
            height: 100%;
            background: linear-gradient(45deg, #FF6B35 0%, #F7931E 100%);
            clip-path: polygon(30% 0%, 100% 0%, 100% 100%, 0% 100%);
            z-index: 1;
        }

        /* Right Panel - Light Theme */
        .right-panel {
            flex: 1;
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(15px);
            padding: clamp(1rem, 2vw, 1.5rem);
            overflow-y: auto;
            max-height: 100%;
        }

        /* Calendar Header Styling */
        .calendar-header {
            z-index: 2;
            position: relative;
        }

        .calendar-title {
            font-size: clamp(2rem, 4vw, 3rem);
            font-weight: 700;
            margin: 0;
            letter-spacing: 2px;
        }

        .calendar-subtitle {
            font-size: clamp(1rem, 2vw, 1.5rem);
            font-weight: 400;
            margin: 0.5rem 0 0 0;
            opacity: 0.9;
        }

        .highlight {
            color: #FF6B35;
            font-weight: 600;
        }

        /* Month Navigation */
        .month-display {
            z-index: 2;
            position: relative;
            margin: clamp(1.5rem, 3vw, 2rem) 0;
        }

        .month-navigation {
            display: flex;
            align-items: center;
            justify-content: space-between;
            max-width: 300px;
        }

        .current-month {
            font-size: clamp(1.2rem, 2.5vw, 1.8rem);
            font-weight: 600;
            margin: 0;
            color: #FF6B35;
        }

        .nav-arrow {
            font-size: clamp(1.5rem, 3vw, 2rem);
            cursor: pointer;
            padding: 0.5rem;
            border-radius: 50%;
            transition: all 0.3s ease;
            user-select: none;
        }

        .nav-arrow:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: scale(1.1);
        }

        /* Main Calendar Container */
        .main-calendar-container {
            flex: 1;
            z-index: 2;
            position: relative;
            display: flex;
            flex-direction: column;
            justify-content: center;
        }

        .main-calendar-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            gap: clamp(0.3rem, 0.8vw, 0.6rem);
            max-width: 100%;
        }

        /* Day Headers */
        .day-header {
            color: #FF6B35;
            font-weight: 600;
            font-size: clamp(0.8rem, 1.5vw, 1rem);
            text-align: center;
            padding: 0.5rem 0;
            margin-bottom: 0.5rem;
        }

        /* Date Cell Styling */
        .date-cell {
            font-size: clamp(1rem, 1.8vw, 1.3rem);
            font-weight: 500;
            color: rgba(255, 255, 255, 0.8);
            padding: clamp(0.4rem, 0.8vw, 0.6rem);
            display: flex;
            justify-content: center;
            align-items: center;
            height: clamp(35px, 6vw, 45px);
            border-radius: 8px;
            transition: all 0.3s ease;
            position: relative;
            text-align: center;
        }

        .date-cell.clickable {
            cursor: pointer;
            user-select: none;
            color: white;
        }

        .date-cell.clickable:hover {
            background-color: rgba(255, 255, 255, 0.1);
            transform: translateY(-2px);
            color: #FF6B35;
        }

        .date-cell.inactive {
            color: rgba(255, 255, 255, 0.3);
        }

        .date-cell.sunday {
            color: #FF6B35;
        }

        .date-cell.featured {
            background-color: #FF6B35;
            color: white;
            font-weight: 600;
            border-radius: 8px;
        }

        .date-cell.featured:hover {
            background-color: #E55A2B;
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
        }

        .date-cell.highlighted {
            background-color: white;
            color: #2D1B69;
            font-weight: 700;
            border-radius: 8px;
            box-shadow: 0 4px 15px rgba(255, 255, 255, 0.3);
        }

        .date-cell.selected {
            background-color: #FF6B35;
            color: white;
            font-weight: 600;
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
        }

        /* Contact Info */
        .contact-info {
            z-index: 2;
            position: relative;
            font-size: clamp(0.8rem, 1.2vw, 0.9rem);
            opacity: 0.8;
        }

        .contact-info p {
            margin: 0.2rem 0;
        }

        /* Right Panel Styling */
        .mini-calendar {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 1rem;
            margin-bottom: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .mini-calendar h3 {
            color: #2D1B69;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
        }

        .mini-grid {
            display: grid;
            grid-template-columns: repeat(7, 1fr);
            text-align: center;
            gap: 0.5rem;
            font-size: 0.8rem;
            color: #2D1B69;
        }

        .mini-grid span:nth-child(-n+7) {
            font-weight: 600;
            color: #FF6B35;
            font-size: 0.7rem;
            margin-bottom: 0.5rem;
        }

        .mini-grid .sunday {
            color: #FF6B35;
        }

        .mini-grid .inactive {
            color: rgba(45, 27, 105, 0.3);
        }

        /* Form Styling */
        .reminder-form-section {
            margin-bottom: 1.5rem;
        }

        .reminder-form {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 15px;
            padding: 1.5rem;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.3);
        }

        .form-title {
            color: #2D1B69;
            font-weight: 600;
            font-size: 1rem;
            margin-bottom: 1rem;
            text-align: center;
        }

        .form-input {
            width: 100%;
            padding: 0.8rem;
            border: 2px solid rgba(45, 27, 105, 0.2);
            border-radius: 10px;
            background: rgba(255, 255, 255, 0.9);
            color: #2D1B69;
            font-size: 0.9rem;
            margin-bottom: 1rem;
            transition: all 0.3s ease;
        }

        .form-input:focus {
            outline: none;
            border-color: #FF6B35;
            box-shadow: 0 0 0 3px rgba(255, 107, 53, 0.1);
        }

        .form-row {
            display: flex;
            gap: 1rem;
            align-items: flex-end;
        }

        .add-btn {
            background: linear-gradient(135deg, #FF6B35 0%, #F7931E 100%);
            color: white;
            border: none;
            border-radius: 10px;
            width: 50px;
            height: 50px;
            font-size: 1.5rem;
            font-weight: bold;
            cursor: pointer;
            transition: all 0.3s ease;
            flex-shrink: 0;
        }

        .add-btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 15px rgba(255, 107, 53, 0.4);
        }

        /* Reminder List Styling */
        .reminder-list-section {
            margin-bottom: 1.5rem;
        }

        .reminder-list-title {
            color: #2D1B69;
            font-size: 1.1rem;
            font-weight: 600;
            margin-bottom: 1rem;
            text-align: center;
            border-bottom: 2px solid rgba(255, 107, 53, 0.3);
            padding-bottom: 0.5rem;
        }

        .reminder-list-container {
            max-height: 200px;
            overflow-y: auto;
            padding-right: 0.5rem;
        }

        .reminder-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            border-left: 4px solid #FF6B35;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
            font-size: 0.9rem;
            line-height: 1.4;
        }

        .reminder-item:hover {
            transform: translateX(3px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .reminder-number {
            color: #FF6B35;
            font-weight: 600;
            margin-right: 0.5rem;
        }

        .reminder-date-time {
            color: #2D1B69;
            font-size: 0.8rem;
            margin-top: 0.3rem;
            font-weight: 500;
        }

        /* Activity Display Styling */
        .activity-display {
            background: rgba(255, 255, 255, 0.9);
            backdrop-filter: blur(15px);
            border-radius: 15px;
            padding: 1.5rem;
            margin-bottom: 1.5rem;
            border: 1px solid rgba(255, 255, 255, 0.3);
            box-shadow: 0 8px 25px rgba(0, 0, 0, 0.1);
            animation: slideIn 0.3s ease-out;
        }

        @keyframes slideIn {
            from {
                opacity: 0;
                transform: translateY(20px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }

        .activity-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 1rem;
            padding-bottom: 0.5rem;
            border-bottom: 2px solid rgba(255, 107, 53, 0.3);
        }

        .activity-header h3 {
            color: #2D1B69;
            font-size: 1.2rem;
            font-weight: 600;
            margin: 0;
        }

        .close-activity-btn {
            background: none;
            border: none;
            font-size: 1.5rem;
            color: #FF6B35;
            cursor: pointer;
            padding: 0.3rem 0.6rem;
            border-radius: 50%;
            transition: all 0.2s ease;
        }

        .close-activity-btn:hover {
            background-color: rgba(255, 107, 53, 0.1);
            transform: scale(1.1);
        }

        .activity-list {
            min-height: 100px;
        }

        .activity-item {
            background: rgba(255, 255, 255, 0.8);
            border-radius: 10px;
            padding: 1rem;
            margin-bottom: 0.8rem;
            border-left: 4px solid #FF6B35;
            box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);
            transition: all 0.2s ease;
        }

        .activity-item:hover {
            transform: translateX(5px);
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
        }

        .activity-time {
            color: #FF6B35;
            font-weight: 600;
            font-size: 0.9rem;
            margin-bottom: 0.3rem;
        }

        .activity-name {
            color: #2D1B69;
            font-size: 1rem;
            font-weight: 500;
        }

        .no-activities {
            text-align: center;
            color: rgba(45, 27, 105, 0.6);
            font-style: italic;
            padding: 2rem;
            font-size: 1rem;
        }

        /* Mobile Responsive Design */
        @media (max-width: 768px) {
            body {
                padding: 1rem;
                height: 100vh;
                overflow: hidden;
            }

            .modern-calendar-layout {
                flex-direction: column;
                height: calc(100vh - 2rem);
                gap: 0;
            }

            .left-panel {
                flex: 0 0 65%;
                padding: 1.5rem 1rem;
            }

            .left-panel::before {
                width: 60px;
                clip-path: polygon(50% 0%, 100% 0%, 100% 100%, 0% 100%);
            }

            .right-panel {
                flex: 1;
                padding: 1rem;
                overflow-y: auto;
            }

            .calendar-title {
                font-size: 2rem;
            }

            .calendar-subtitle {
                font-size: 1rem;
            }

            .current-month {
                font-size: 1.3rem;
            }

            .main-calendar-grid {
                gap: 0.2rem;
            }

            .date-cell {
                height: 30px;
                font-size: 0.9rem;
            }

            .day-header {
                font-size: 0.8rem;
                padding: 0.3rem 0;
                margin-bottom: 0.3rem;
            }

            .mini-calendar, .reminder-form, .activity-display {
                padding: 1rem;
                margin-bottom: 1rem;
            }

            .form-input {
                padding: 0.6rem;
                font-size: 0.8rem;
            }

            .add-btn {
                width: 40px;
                height: 40px;
                font-size: 1.2rem;
            }
        }

        /* Tablet Responsive Design */
        @media (max-width: 1024px) and (min-width: 769px) {
            .modern-calendar-layout {
                height: calc(100vh - 4rem);
            }

            .left-panel {
                flex: 0 0 55%;
                padding: 2rem 1.5rem;
            }

            .right-panel {
                flex: 1;
                padding: 1.5rem;
            }

            .main-calendar-grid {
                gap: 0.4rem;
            }

            .date-cell {
                height: 40px;
                font-size: 1.1rem;
            }
        }

        /* Desktop Optimizations */
        @media (min-width: 1025px) {
            body {
                padding: clamp(0.5rem, 1vw, 1rem);
                height: 100vh;
                overflow: hidden;
            }

            .modern-calendar-layout {
                height: calc(100vh - 2rem);
            }

            .left-panel {
                flex: 0 0 60%;
                padding: clamp(2rem, 3vw, 3rem);
            }

            .main-calendar-grid {
                gap: clamp(0.5rem, 1vw, 0.8rem);
            }

            .date-cell {
                height: clamp(40px, 6vw, 50px);
                font-size: clamp(1.1rem, 1.8vw, 1.4rem);
            }

            .reminder-list-container {
                max-height: 250px;
            }
        }

        /* Large Desktop Optimizations */
        @media (min-width: 1400px) {
            .left-panel {
                padding: 3rem;
            }

            .right-panel {
                padding: 2rem;
            }

            .calendar-title {
                font-size: 3.5rem;
            }

            .calendar-subtitle {
                font-size: 1.8rem;
            }

            .current-month {
                font-size: 2rem;
            }

            .main-calendar-grid {
                gap: 1rem;
            }

            .date-cell {
                height: 55px;
                font-size: 1.5rem;
            }
        }

        /* Prevent horizontal scrolling */
        * {
            box-sizing: border-box;
        }

        html, body {
            overflow-x: hidden;
            max-width: 100vw;
        }

        .modern-calendar-layout {
            max-width: 100vw;
            overflow: hidden;
        }
    </style>

    <!-- Interactive JavaScript Functionality -->
    <script>
        // Sample activity data - in a real application, this would come from a database
        const sampleActivities = {
            '08-01': [
                { time: '09:00', name: 'Rapat Tim Proyek' },
                { time: '14:00', name: 'Presentasi Klien' }
            ],
            '08-05': [
                { time: '10:30', name: 'Training Karyawan Baru' },
                { time: '15:00', name: 'Review Dokumen Teknis' }
            ]
        };

        // Reminder list data - includes important dates and holidays
        let reminderList = [
            {
                id: 1,
                name: 'Meeting dengan Vendor',
                date: '2025-08-12',
                time: '08:20',
                type: 'meeting'
            },
            {
                id: 2,
                name: 'Audit Internal',
                date: '2025-08-20',
                time: '09:00',
                type: 'meeting'
            }
        ];

        // Function to format date for display
        function formatDateForDisplay(dateStr) {
            const date = new Date(dateStr);
            const months = [
                'Januari', 'Februari', 'Maret', 'April', 'Mei', 'Juni',
                'Juli', 'Agustus', 'September', 'Oktober', 'November', 'Desember'
            ];
            return `${date.getDate()} ${months[date.getMonth()]} ${date.getFullYear()}`;
        }

        // Function to render reminder list
        function renderReminderList() {
            const reminderListContainer = document.getElementById('reminderListDisplay');

            if (reminderList.length === 0) {
                reminderListContainer.innerHTML = '<div class="reminder-item">Belum ada reminder yang ditambahkan</div>';
                return;
            }

            // Sort reminders by date
            const sortedReminders = [...reminderList].sort((a, b) => new Date(a.date) - new Date(b.date));

            reminderListContainer.innerHTML = sortedReminders.map((reminder, index) => `
                <div class="reminder-item">
                    <span class="reminder-number">${index + 1}.${reminder.name}</span>
                    <div class="reminder-date-time">${formatDateForDisplay(reminder.date)} Pukul ${reminder.time}</div>
                </div>
            `).join('');
        }

        document.addEventListener('DOMContentLoaded', function () {
            const clickableDates = document.querySelectorAll('.date-cell.clickable');
            const activityDisplay = document.getElementById('activityDisplay');
            const selectedDateTitle = document.getElementById('selectedDateTitle');
            const activityList = document.getElementById('activityList');
            const closeActivityBtn = document.getElementById('closeActivityBtn');
            const reminderForm = document.getElementById('reminderForm');
            const prevMonthBtn = document.getElementById('prevMonth');
            const nextMonthBtn = document.getElementById('nextMonth');
            const currentMonthDisplay = document.querySelector('.current-month');

            // Initialize reminder list display
            renderReminderList();

            // Month navigation functionality
            if (prevMonthBtn && nextMonthBtn) {
                prevMonthBtn.addEventListener('click', function() {
                    // Add month navigation logic here
                    console.log('Previous month clicked');
                });

                nextMonthBtn.addEventListener('click', function() {
                    // Add month navigation logic here
                    console.log('Next month clicked');
                });
            }

            // Add click event listeners to all clickable date cells
            clickableDates.forEach(dateCell => {
                dateCell.addEventListener('click', function () {
                    // Remove previous selection
                    document.querySelectorAll('.date-cell.selected').forEach(cell => {
                        cell.classList.remove('selected');
                    });

                    // Add selection to clicked date
                    this.classList.add('selected');

                    // Get date information
                    const date = this.getAttribute('data-date');
                    const month = this.getAttribute('data-month');
                    const dateKey = `${month}-${date.padStart(2, '0')}`;

                    // Update title
                    const monthNames = {
                        '08': 'Agustus',
                        '07': 'Juli',
                        '09': 'September'
                    };
                    selectedDateTitle.textContent = `Kegiatan untuk ${date} ${monthNames[month] || 'Agustus'} 2025`;

                    // Get activities for this date
                    const activities = sampleActivities[dateKey] || [];

                    // Populate activity list
                    if (activities.length > 0) {
                        activityList.innerHTML = activities.map(activity => `
                            <div class="activity-item">
                                <div class="activity-time">${activity.time}</div>
                                <div class="activity-name">${activity.name}</div>
                            </div>
                        `).join('');
                    } else {
                        activityList.innerHTML = '<div class="no-activities">Tidak ada kegiatan terjadwal untuk tanggal ini</div>';
                    }

                    // Show activity display
                    activityDisplay.style.display = 'block';

                    // Smooth scroll to activity display
                    setTimeout(() => {
                        activityDisplay.scrollIntoView({
                            behavior: 'smooth',
                            block: 'nearest'
                        });
                    }, 100);
                });
            });

            // Close activity display
            closeActivityBtn.addEventListener('click', function () {
                activityDisplay.style.display = 'none';
                // Remove selection from all dates
                document.querySelectorAll('.date-cell.selected').forEach(cell => {
                    cell.classList.remove('selected');
                });
            });

            // Handle reminder form submission
            reminderForm.addEventListener('submit', function (e) {
                e.preventDefault();

                const activityName = document.getElementById('activityName').value.trim();
                const activityDate = document.getElementById('activityDate').value;
                const activityTime = document.getElementById('activityTime').value;

                if (activityName && activityDate && activityTime) {
                    // Add new reminder to the list
                    const newReminder = {
                        id: reminderList.length + 1,
                        name: activityName,
                        date: activityDate,
                        time: activityTime,
                        type: 'user-added'
                    };

                    reminderList.push(newReminder);

                    // Re-render the reminder list
                    renderReminderList();

                    // Clear the form
                    reminderForm.reset();

                    // Show success feedback (optional)
                    const button = document.getElementById('addReminderBtn');
                    const originalText = button.textContent;
                    button.textContent = '✓';
                    button.style.backgroundColor = '#22c55e';

                    setTimeout(() => {
                        button.textContent = originalText;
                        button.style.backgroundColor = '';
                    }, 1500);
                }
            });

            // Add visual feedback for dates with activities
            function updateActivityIndicators() {
                // Clear existing indicators
                document.querySelectorAll('.activity-indicator').forEach(indicator => {
                    indicator.remove();
                });

                // Add indicators for dates with activities
                Object.keys(sampleActivities).forEach(dateKey => {
                    const [month, date] = dateKey.split('-');
                    const dateCell = document.querySelector(`[data-date="${parseInt(date)}"][data-month="${month}"]`);
                    if (dateCell && dateCell.classList.contains('clickable')) {
                        // Add a small indicator for dates with activities
                        const indicator = document.createElement('div');
                        indicator.className = 'activity-indicator';
                        indicator.style.cssText = `
                            position: absolute;
                            top: 5px;
                            right: 5px;
                            width: 6px;
                            height: 6px;
                            background-color: var(--red);
                            border-radius: 50%;
                            box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                        `;
                        dateCell.style.position = 'relative';
                        dateCell.appendChild(indicator);
                    }
                });

                // Add indicators for reminder dates
                reminderList.forEach(reminder => {
                    const reminderDate = new Date(reminder.date);
                    const month = String(reminderDate.getMonth() + 1).padStart(2, '0');
                    const date = reminderDate.getDate();

                    const dateCell = document.querySelector(`[data-date="${date}"][data-month="${month}"]`);
                    if (dateCell && dateCell.classList.contains('clickable')) {
                        // Check if indicator already exists
                        if (!dateCell.querySelector('.activity-indicator')) {
                            const indicator = document.createElement('div');
                            indicator.className = 'activity-indicator';
                            indicator.style.cssText = `
                                position: absolute;
                                top: 5px;
                                right: 5px;
                                width: 6px;
                                height: 6px;
                                background-color: var(--purple);
                                border-radius: 50%;
                                box-shadow: 0 1px 3px rgba(0,0,0,0.3);
                            `;
                            dateCell.style.position = 'relative';
                            dateCell.appendChild(indicator);
                        }
                    }
                });
            }

            // Initial call to set up indicators
            updateActivityIndicators();

            // Update indicators when new reminders are added
            const originalRenderReminderList = renderReminderList;
            renderReminderList = function () {
                originalRenderReminderList();
                updateActivityIndicators();
            };
        });
    </script>
</body>

</html>