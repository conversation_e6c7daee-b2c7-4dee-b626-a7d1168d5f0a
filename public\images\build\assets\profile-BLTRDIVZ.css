.container{width:100%;max-width:100%}.main-header{display:flex;align-items:center;gap:.1rem;margin-bottom:.5rem}.logo img{width:160px;height:auto}.company-title h1{font-size:2.25rem;font-weight:700;color:var(--brand-purple);line-height:1.2;margin:0}.tagline{font-size:.9rem;color:var(--text-light);margin:.5rem 0 0}.content-grid{gap:2rem}.left-column,.right-column{display:flex;flex-direction:column;gap:2rem}.about-us p{font-size:1rem;line-height:1.6;margin:0}.card h2{font-size:1.25rem;font-weight:600;color:var(--brand-purple);text-align:center;margin-top:0;margin-bottom:1.5rem}.vision-card p{text-align:center;font-style:italic;line-height:1.7;margin:0}.partners-grid{display:grid;grid-template-columns:1fr 1fr;gap:1rem}.partner-item{display:flex;align-items:center;gap:1rem}.partner-item img{height:40px;width:40px;-o-object-fit:contain;object-fit:contain}.partner-info{display:flex;flex-direction:column}.partner-info p{font-size:.7rem;margin:0}.join-us .question-mark{width:40px;height:40px;flex-shrink:0;background-color:#d1d1d1;border-radius:50%;display:flex;align-items:center;justify-content:center;font-size:1.5rem;font-weight:600;color:#fff}.mission-card ul{padding:0;margin:0;display:flex;flex-direction:column;gap:.5rem}.mission-card li{display:flex;align-items:flex-start;font-size:.9rem;line-height:1.4}.mission-card li:before{content:"✔";display:inline-block;width:22px;height:22px;flex-shrink:0;border-radius:50%;background-color:var(--brand-purple);color:#192463;font-size:14px;text-align:center;line-height:23px;margin-top:2px}.main-footer{display:flex;justify-content:space-between;align-items:center;gap:2rem;margin-top:1rem;border-top:1px solid var(--border-color)}.contact-item{display:flex;align-items:center;gap:1rem}.contact-item .icon{width:40px;height:40px}.contact-item p{margin:0;line-height:1.4;font-size:.9rem}.contact-item p strong{font-weight:600}@media (max-width: 1024px){.content-grid{grid-template-columns:1fr}}@media (max-width: 768px){body{padding:1rem}.container{padding:0}.main-header{flex-direction:column;text-align:center;gap:.2rem}.main-footer{flex-direction:column;align-items:flex-start}.partners-grid{grid-template-columns:1fr}.home-button{bottom:1rem;right:1rem;width:60px;height:60px}.home-button svg{width:24px;height:24px}}
